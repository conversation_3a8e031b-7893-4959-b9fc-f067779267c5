// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'live_stream_api.dart';

// **************************************************************************
// ChopperGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
final class _$LiveStreamApi extends LiveStreamApi {
  _$LiveStreamApi([ChopperClient? client]) {
    if (client == null) return;
    this.client = client;
  }

  @override
  final Type definitionType = LiveStreamApi;

  @override
  Future<Response<dynamic>> startLiveStream(Map<String, dynamic> body) {
    final Uri $url = Uri.parse('live-stream/start');
    final $body = body;
    final Request $request = Request(
      'POST',
      $url,
      client.baseUrl,
      body: $body,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> endLiveStream(String id) {
    final Uri $url = Uri.parse('live-stream/${id}/end');
    final Request $request = Request(
      'POST',
      $url,
      client.baseUrl,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> getActiveLiveStreams(Map<String, dynamic> query) {
    final Uri $url = Uri.parse('live-stream/active');
    final Map<String, dynamic> $params = query;
    final Request $request = Request(
      'GET',
      $url,
      client.baseUrl,
      parameters: $params,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> getLiveStream(String id) {
    final Uri $url = Uri.parse('live-stream/${id}');
    final Request $request = Request(
      'GET',
      $url,
      client.baseUrl,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> joinLiveStream(String id) {
    final Uri $url = Uri.parse('live-stream/${id}/join');
    final Request $request = Request(
      'POST',
      $url,
      client.baseUrl,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> leaveLiveStream(String id) {
    final Uri $url = Uri.parse('live-stream/${id}/leave');
    final Request $request = Request(
      'POST',
      $url,
      client.baseUrl,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> getParticipants(
    String id,
    Map<String, dynamic> query,
  ) {
    final Uri $url = Uri.parse('live-stream/${id}/participants');
    final Map<String, dynamic> $params = query;
    final Request $request = Request(
      'GET',
      $url,
      client.baseUrl,
      parameters: $params,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> inviteUsers(
    String id,
    Map<String, dynamic> body,
  ) {
    final Uri $url = Uri.parse('live-stream/${id}/invite');
    final $body = body;
    final Request $request = Request(
      'POST',
      $url,
      client.baseUrl,
      body: $body,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> banUser(
    String id,
    Map<String, dynamic> body,
  ) {
    final Uri $url = Uri.parse('live-stream/${id}/ban');
    final $body = body;
    final Request $request = Request(
      'POST',
      $url,
      client.baseUrl,
      body: $body,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> unbanUser(
    String id,
    Map<String, dynamic> body,
  ) {
    final Uri $url = Uri.parse('live-stream/${id}/unban');
    final $body = body;
    final Request $request = Request(
      'POST',
      $url,
      client.baseUrl,
      body: $body,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> pinMessage(
    String id,
    Map<String, dynamic> body,
  ) {
    final Uri $url = Uri.parse('live-stream/${id}/pin-message');
    final $body = body;
    final Request $request = Request(
      'POST',
      $url,
      client.baseUrl,
      body: $body,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> addComment(
    String id,
    Map<String, dynamic> body,
  ) {
    final Uri $url = Uri.parse('live-stream/${id}/comment');
    final $body = body;
    final Request $request = Request(
      'POST',
      $url,
      client.baseUrl,
      body: $body,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> getComments(
    String id,
    Map<String, dynamic> query,
  ) {
    final Uri $url = Uri.parse('live-stream/${id}/comments');
    final Map<String, dynamic> $params = query;
    final Request $request = Request(
      'GET',
      $url,
      client.baseUrl,
      parameters: $params,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> addReaction(
    String id,
    Map<String, dynamic> body,
  ) {
    final Uri $url = Uri.parse('live-stream/${id}/react');
    final $body = body;
    final Request $request = Request(
      'POST',
      $url,
      client.baseUrl,
      body: $body,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> getReactions(
    String id,
    Map<String, dynamic> query,
  ) {
    final Uri $url = Uri.parse('live-stream/${id}/reactions');
    final Map<String, dynamic> $params = query;
    final Request $request = Request(
      'GET',
      $url,
      client.baseUrl,
      parameters: $params,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> getAgoraToken(String id) {
    final Uri $url = Uri.parse('live-stream/${id}/agora-token');
    final Request $request = Request(
      'GET',
      $url,
      client.baseUrl,
    );
    return client.send<dynamic, dynamic>($request);
  }
}
